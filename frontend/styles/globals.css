@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: none; }
}

:root {
  --ss-white: #ffffff;
  --ss-black: #000000;
  --ss-primary: #0055A5;
  --ss-accent: #FFC200;
  --ss-red: #EF4444;
  --ss-green: #22C55E;
  --ss-yellow: #EAB308;
  --ss-blue: #2563EB;

  /* Background & surface shades */
  --bg-light: #F9FAFB;
  --bg-default: #F9FAFB;
  --bg-dark: #121212;
  --surface-default: #FFFFFF;
  --surface-muted: #E5E7EB;

  /* Text colors */
  --text-primary-light: #1F2D3D;
  --text-primary-dark: #E4E4E7;

  /* Accent */
  --accent: var(--ss-accent);
  /* Accent hover */
  --accent-hover: #E6A700;
}

html {
  font-family: 'Inter', system-ui, sans-serif;
  color-scheme: light dark;
  background: linear-gradient(to bottom right, #dbeafe 0%, #fff 50%, #bfdbfe 100%);
  color: var(--text-primary-light);
  transition: background-color 0.2s ease, color 0.2s ease;
}

html.dark {
  background: var(--bg-dark);
  color: var(--text-primary-dark);
}

html, body, #__next {
  height: 100%;
  min-height: 100vh;
}

/* Base body styles using variables */
body {
  background: none;
  color: var(--text-primary-light);
  transition: background-color 0.2s ease, color 0.2s ease;
}

html.dark body {
  background: none;
  --bg-default: var(--bg-dark);
  color: var(--text-primary-dark);
}

/* Responsive utilities */
.container-responsive {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 2rem;
  }
}

/* Touch-friendly utilities */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.touch-manipulation {
  touch-action: manipulation;
}

/* Mobile-first responsive text */
.text-responsive {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: 1rem;
  }
}

/* Mobile-optimized spacing */
.space-mobile {
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .space-mobile {
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .space-mobile {
    gap: 1.5rem;
  }
}

/* Table responsive styles */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  min-width: 100%;
  white-space: nowrap;
}

@media (max-width: 1023px) {
  .table-responsive table {
    min-width: 800px;
  }
}

.animate-fade-in {
  animation: fadeIn 0.15s ease;
}
.bg-primary { background-color: var(--ss-primary) !important; }
.text-primary { color: var(--ss-primary) !important; }
.bg-accent { background-color: var(--ss-accent) !important; }
.text-accent { color: var(--ss-accent) !important; }
.bg-red { background-color: var(--ss-red) !important; }
.bg-green { background-color: var(--ss-green) !important; }
.bg-yellow { color: var(--ss-yellow) !important; }
.bg-blue { background-color: var(--ss-blue) !important; }

a {
  color: var(--accent);
  transition: color 0.2s;
}
a:hover {
  color: var(--accent-hover);
}

/* Enhanced FullCalendar responsive styles */
.fc {
  font-family: inherit;
  font-size: 1rem;
}
.fc .fc-toolbar-title {
  font-weight: 600;
  color: var(--ss-primary);
}
.fc .fc-button {
  background: var(--ss-primary);
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.25rem 0.75rem;
  margin: 0 0.125rem;
  transition: background 0.2s;
}
.fc .fc-button:hover, .fc .fc-button:focus {
  background: var(--ss-accent);
  color: #000;
}
.fc .fc-daygrid-day-number {
  color: var(--ss-primary);
  font-weight: 500;
}
.fc .fc-day-today {
  background: var(--ss-accent);
  color: #000;
}

/* Responsive calendar adjustments */
@media (max-width: 768px) {
  .fc .fc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .fc .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }
  
  .fc .fc-button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .fc .fc-daygrid-event {
    font-size: 0.75rem;
    padding: 1px 2px;
  }
  
  .fc .fc-col-header-cell {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .fc .fc-toolbar-title {
    font-size: 1.125rem;
  }
  
  .fc .fc-button {
    padding: 0.125rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .fc .fc-daygrid-event {
    font-size: 0.625rem;
  }
}

/* Dark mode calendar adjustments */
html.dark .fc {
  color: var(--text-primary-dark);
}

html.dark .fc .fc-toolbar-title {
  color: var(--ss-accent);
}

html.dark .fc .fc-col-header-cell,
html.dark .fc .fc-daygrid-day-number {
  color: var(--text-primary-dark);
}

html.dark .fc .fc-day-today {
  background: var(--ss-accent);
  color: var(--ss-black);
}

html.dark .fc .fc-daygrid-day {
  border-color: rgba(255, 255, 255, 0.1);
}

html.dark .fc .fc-scrollgrid {
  border-color: rgba(255, 255, 255, 0.1);
}

.text-primary-dark {
  color: var(--text-primary-dark) !important;
}
.text-primary-light {
  color: var(--text-primary-light) !important;
}
.text-gray-dark {
  color: #1F2D3D !important;
}
.text-gray-light {
  color: #E4E4E7 !important;
}